#===============================================================================
# Enhanced Bridge-Aware NPCs for Pokemon Essentials
# Automatically manages NPC behavior and rendering based on bridge states
# Supports environmental effects preservation and advanced z-ordering
#===============================================================================

module BridgeAwareNPCs
  # Constants for z-level adjustments (per bridge height level)
  BRIDGE_HEIGHT_MULTIPLIER = 32  # Pixels per bridge height level (standard RPG Maker tile height)
  BASE_BRIDGE_OFFSET = 32        # Base offset for bridge positioning
  UNDER_BRIDGE_OFFSET = -32      # Offset for under-bridge positioning

  # Environmental effects constants
  GRASS_OVERLAY_Z_OFFSET = 32   # Additional z-offset for grass overlays on bridge NPCs
  WATER_EFFECT_Z_OFFSET = 16    # Additional z-offset for water effects on bridge NPCs

  # Performance optimization
  @@last_bridge_state = nil
  @@last_bridge_height = nil
  @@cached_bridge_npcs = []
  @@cache_dirty = true

  # Calculate z-offset based on bridge height
  def self.calculate_bridge_z_offset(bridge_height, is_on_bridge, is_active)
    return 0 if bridge_height == 0 && !is_on_bridge

    if is_on_bridge
      if is_active
        # On-bridge NPCs: elevated by bridge height + 1 additional level for proper layering
        return (bridge_height + 1) * BRIDGE_HEIGHT_MULTIPLIER + BASE_BRIDGE_OFFSET
      else
        # Bridge is off: on-bridge NPCs float high above
        return (bridge_height + 3) * BRIDGE_HEIGHT_MULTIPLIER + BASE_BRIDGE_OFFSET
      end
    else
      if is_active
        # Under-bridge NPCs: below bridge surface
        return UNDER_BRIDGE_OFFSET
      else
        # Bridge is off: under-bridge NPCs at normal level
        return 0
      end
    end
  end

  # Get current bridge height
  def self.bridge_height
    return 0 if !$PokemonGlobal || !$PokemonGlobal.respond_to?(:bridge)
    return $PokemonGlobal.bridge || 0
  end

  # Check if an event has bridge-aware naming
  def self.is_bridge_aware?(event)
    return false if !event || !event.respond_to?(:name) || !event.name
    name = event.name.downcase
    # Check for z-level naming patterns
    return true if name.include?("z100") || name.include?("z0")
    return true if name.include?("onz") || name.include?("offz")
    return false
  rescue
    return false
  end

  # Determine if NPC should be "on" the bridge based on naming
  def self.is_on_bridge_npc?(event)
    return false if !event || !event.respond_to?(:name) || !event.name
    name = event.name.downcase
    # NPCs with z100 or OnZ are considered "on bridge" NPCs
    return true if name.include?("z100") || name.include?("onz")
    return false
  rescue
    return false
  end

  # Determine if NPC should be "under" the bridge based on naming
  def self.is_under_bridge_npc?(event)
    return false if !event || !event.respond_to?(:name) || !event.name
    name = event.name.downcase
    # NPCs with z0 or OffZ are considered "under bridge" NPCs
    return true if name.include?("z0") || name.include?("offz")
    return false
  rescue
    return false
  end

  # Get current bridge state
  def self.bridge_active?
    return bridge_height > 0
  end

  # Check if NPC should preserve environmental effects
  def self.should_preserve_effects?(event)
    return false if !event || !event.respond_to?(:name) || !event.name
    name = event.name.downcase
    # Check for special flags that indicate environmental effects should be preserved
    return true if name.include?("grassfx") || name.include?("waterfx") || name.include?("envfx")
    return false
  rescue
    return false
  end

  # Get environmental effect z-offset for an NPC
  def self.get_environmental_z_offset(event)
    return 0 if !should_preserve_effects?(event)

    terrain = event.terrain_tag
    return GRASS_OVERLAY_Z_OFFSET if terrain.shows_grass_rustle
    return WATER_EFFECT_Z_OFFSET if terrain.can_surf || terrain.shows_reflections
    return 0
  end

  # Update NPC properties based on bridge state
  def self.update_npc_for_bridge(event)
    return if !event || !event.respond_to?(:name)
    return if !is_bridge_aware?(event)
    return if !$PokemonGlobal || !$PokemonGlobal.respond_to?(:bridge)
    return if !$game_map || !$game_map.events || !$game_map.events[event.id]

    bridge_active = bridge_active?
    current_bridge_height = bridge_height
    env_z_offset = get_environmental_z_offset(event)

    if is_on_bridge_npc?(event)
      # NPC that should be on the bridge
      bridge_z = calculate_bridge_z_offset(current_bridge_height, true, bridge_active)

      if bridge_active
        # Bridge is on - NPC is interactable at bridge level
        event.through = false
        event.instance_variable_set(:@bridge_z_offset, bridge_z + env_z_offset)
        event.instance_variable_set(:@bridge_disabled, false)
        event.instance_variable_set(:@bridge_type, :on_bridge_active)
      else
        # Bridge is off - NPC is high above and non-interactable
        event.through = true
        event.instance_variable_set(:@bridge_z_offset, bridge_z + env_z_offset)
        event.instance_variable_set(:@bridge_disabled, true)
        event.instance_variable_set(:@bridge_type, :on_bridge_inactive)
      end
    elsif is_under_bridge_npc?(event)
      # NPC that should be under the bridge
      bridge_z = calculate_bridge_z_offset(current_bridge_height, false, bridge_active)

      if bridge_active
        # Bridge is on - NPC is below the bridge level but interactable
        event.through = false
        event.instance_variable_set(:@bridge_z_offset, bridge_z + env_z_offset)
        event.instance_variable_set(:@bridge_disabled, false)
        event.instance_variable_set(:@bridge_type, :under_bridge_active)
      else
        # Bridge is off - NPC is at normal level and interactable
        event.through = false
        event.instance_variable_set(:@bridge_z_offset, bridge_z + env_z_offset)
        event.instance_variable_set(:@bridge_disabled, false)
        event.instance_variable_set(:@bridge_type, :under_bridge_inactive)
      end
    end

    # Store original properties for restoration if needed
    if !event.instance_variable_get(:@bridge_original_through)
      event.instance_variable_set(:@bridge_original_through, event.through)
    end
  end

  # Refresh all bridge-aware NPCs on the current map
  def self.refresh_all_npcs
    return if !$game_map || !$game_map.events

    current_bridge_state = bridge_active?
    current_bridge_height = bridge_height

    # Only refresh if bridge state/height changed or cache is dirty
    if @@last_bridge_state != current_bridge_state || @@last_bridge_height != current_bridge_height || @@cache_dirty
      @@last_bridge_state = current_bridge_state
      @@last_bridge_height = current_bridge_height
      @@cache_dirty = false

      # Update cached list of bridge NPCs
      @@cached_bridge_npcs.clear
      $game_map.events.each_value do |event|
        next if !event || !event.respond_to?(:name)
        if is_bridge_aware?(event)
          @@cached_bridge_npcs.push(event)
          update_npc_for_bridge(event)
        end
      end
    else
      # Quick refresh of cached NPCs only - filter out invalid events
      @@cached_bridge_npcs = @@cached_bridge_npcs.select do |event|
        if event && event.respond_to?(:id) && $game_map.events[event.id]
          update_npc_for_bridge(event)
          true
        else
          false  # Remove invalid events from cache
        end
      end
    end
  rescue => e
    # If there's any error, invalidate cache and try again
    puts "Bridge NPC error: #{e.message}"
    invalidate_cache
    @@last_bridge_state = current_bridge_state
    @@last_bridge_height = current_bridge_height
  end

  # Mark cache as dirty (call when map changes)
  def self.invalidate_cache
    @@cache_dirty = true
    @@cached_bridge_npcs.clear
  end

  # Get bridge NPCs for debugging
  def self.get_bridge_npcs
    return @@cached_bridge_npcs.dup
  end
end

#===============================================================================
# Override pbBridgeOn to refresh NPCs
#===============================================================================
if defined?(pbBridgeOn)
  alias __enhanced_bridge_npcs_pbBridgeOn pbBridgeOn
  def pbBridgeOn(height = 2)
    __enhanced_bridge_npcs_pbBridgeOn(height)
    BridgeAwareNPCs.refresh_all_npcs
  end
end

#===============================================================================
# Override pbBridgeOff to refresh NPCs
#===============================================================================
if defined?(pbBridgeOff)
  alias __enhanced_bridge_npcs_pbBridgeOff pbBridgeOff
  def pbBridgeOff
    __enhanced_bridge_npcs_pbBridgeOff
    BridgeAwareNPCs.refresh_all_npcs
  end
end

#===============================================================================
# Extend Game_Event to handle bridge-aware z-levels and interactions
#===============================================================================
class Game_Event
  # Override refresh to update bridge behavior
  alias __enhanced_bridge_npcs_refresh refresh
  def refresh
    __enhanced_bridge_npcs_refresh
    BridgeAwareNPCs.update_npc_for_bridge(self)
  end

  # Override start to prevent interaction when bridge-disabled
  alias __enhanced_bridge_npcs_start start
  def start
    # Don't start if this NPC is bridge-disabled
    return if @bridge_disabled
    __enhanced_bridge_npcs_start
  end

  # Add method to check bridge type for advanced logic
  def bridge_type
    return @bridge_type || :normal
  end

  # Add method to check if NPC is currently bridge-disabled
  def bridge_disabled?
    return @bridge_disabled || false
  end

  # Add method to get bridge z-offset
  def bridge_z_offset
    return @bridge_z_offset || 0
  end
end

#===============================================================================
# Extend Game_Character to handle proper z-ordering for bridge NPCs and player
#===============================================================================
class Game_Character
  alias __enhanced_bridge_npcs_screen_z screen_z
  def screen_z(height = 0)
    base_z = __enhanced_bridge_npcs_screen_z(height)

    # Special handling for bridge-aware events
    if self.is_a?(Game_Event) && BridgeAwareNPCs.is_bridge_aware?(self)
      bridge_offset = @bridge_z_offset || 0

      # Additional z-ordering logic for complex scenarios
      if @bridge_type == :on_bridge_active && BridgeAwareNPCs.bridge_active?
        # On-bridge NPCs should render above player when player is also on bridge
        if $game_player && $game_player.terrain_tag.bridge
          return base_z + bridge_offset + 1  # Slightly above player
        end
      elsif @bridge_type == :under_bridge_active && BridgeAwareNPCs.bridge_active?
        # Under-bridge NPCs should render below bridge surface
        return base_z + bridge_offset - 1  # Slightly below normal
      end

      return base_z + bridge_offset
    end

    # Special handling for player when bridge is active
    if self.is_a?(Game_Player) && BridgeAwareNPCs.bridge_active?
      # Check if player is on a bridge tile
      if terrain_tag.bridge
        bridge_height = BridgeAwareNPCs.bridge_height
        player_bridge_z = bridge_height * BridgeAwareNPCs::BRIDGE_HEIGHT_MULTIPLIER + BridgeAwareNPCs::BASE_BRIDGE_OFFSET
        return base_z + player_bridge_z
      end
    end

    return base_z
  end
end

#===============================================================================
# Extend Game_Player to handle passability with bridge-disabled NPCs
#===============================================================================
class Game_Player
  alias __enhanced_bridge_npcs_passable? passable?
  def passable?(x, y, d, strict = false)
    # Check normal passability first
    return false unless __enhanced_bridge_npcs_passable?(x, y, d, strict)

    # Check if there are any bridge-disabled events at the target position
    new_x = x + (d == 6 ? 1 : d == 4 ? -1 : 0)
    new_y = y + (d == 2 ? 1 : d == 8 ? -1 : 0)

    return true if !$game_map || !$game_map.events

    $game_map.events.each_value do |event|
      # Check if event is at the target coordinates
      next if event.x != new_x || event.y != new_y
      next if !BridgeAwareNPCs.is_bridge_aware?(event)

      # If event is bridge-disabled, it should be walkable
      next if event.instance_variable_get(:@bridge_disabled)

      # If event is not bridge-disabled and not through, block movement
      return false if !event.through && event.character_name != ""
    end

    return true
  end
end

#===============================================================================
# Environmental Effects Preservation System
#===============================================================================

# Extend Sprite_Character to handle environmental effects on bridge NPCs
class Sprite_Character
  alias __enhanced_bridge_npcs_update update
  def update
    __enhanced_bridge_npcs_update

    # Handle environmental effects for bridge-aware NPCs
    if @character.is_a?(Game_Event) && BridgeAwareNPCs.is_bridge_aware?(@character)
      handle_bridge_environmental_effects
    end
  end

  private

  def handle_bridge_environmental_effects
    return if !BridgeAwareNPCs.should_preserve_effects?(@character)

    # Adjust z-ordering for environmental overlays
    bridge_type = @character.bridge_type
    terrain = @character.terrain_tag

    # Ensure grass overlays render correctly
    if terrain.shows_grass_rustle && (bridge_type == :on_bridge_active || bridge_type == :on_bridge_inactive)
      # Grass effects should render above the NPC when on bridge
      self.z += BridgeAwareNPCs::GRASS_OVERLAY_Z_OFFSET
    end

    # Ensure water effects render correctly
    if (terrain.can_surf || terrain.shows_reflections) && bridge_type == :under_bridge_active
      # Water effects should render below bridge level
      self.z -= BridgeAwareNPCs::WATER_EFFECT_Z_OFFSET
    end
  end
end

# Hook into grass animation system to preserve effects
EventHandlers.add(:on_step_taken, :bridge_grass_rustling,
  proc { |event|
    next if !$scene.is_a?(Scene_Map)
    next if !event.is_a?(Game_Event)
    next if !BridgeAwareNPCs.is_bridge_aware?(event)
    next if !BridgeAwareNPCs.should_preserve_effects?(event)

    event.each_occupied_tile do |x, y|
      terrain = $map_factory.getTerrainTagFromCoords(event.map.map_id, x, y, true)
      next if !terrain.shows_grass_rustle

      # Adjust animation z-level based on bridge state
      z_level = 1
      if event.bridge_type == :on_bridge_active || event.bridge_type == :on_bridge_inactive
        z_level = 4  # Higher z-level for on-bridge NPCs
      elsif event.bridge_type == :under_bridge_active
        z_level = 0  # Lower z-level for under-bridge NPCs
      end

      spriteset = $scene.spriteset(event.map_id)
      spriteset&.addUserAnimation(Settings::GRASS_ANIMATION_ID, x, y, true, z_level)
    end
  }
)

#===============================================================================
# Map Change and Cache Management
#===============================================================================

# Hook into map changes to invalidate cache
EventHandlers.add(:on_enter_map, :bridge_npcs_map_change,
  proc { |old_map_id, new_map_id|
    BridgeAwareNPCs.invalidate_cache
    # Refresh NPCs on new map after a short delay to ensure map is fully loaded
    pbWait(1)
    BridgeAwareNPCs.refresh_all_npcs
  }
)

# Hook into game save/load to ensure proper state
EventHandlers.add(:on_game_load, :bridge_npcs_game_load,
  proc {
    BridgeAwareNPCs.invalidate_cache
    BridgeAwareNPCs.refresh_all_npcs if $game_map
  }
)

#===============================================================================
# Testing and Debugging Utilities
#===============================================================================

# Test function to display bridge NPC information
def pbTestEnhancedBridgeNPCs
  return if !$game_map || !$game_map.events

  bridge_state = BridgeAwareNPCs.bridge_active? ? "ON" : "OFF"
  bridge_height = BridgeAwareNPCs.bridge_height
  npc_count = 0
  messages = []

  $game_map.events.each_value do |event|
    next if !BridgeAwareNPCs.is_bridge_aware?(event)
    npc_count += 1

    disabled = event.bridge_disabled?
    through = event.through
    z_offset = event.bridge_z_offset
    bridge_type = event.bridge_type
    env_effects = BridgeAwareNPCs.should_preserve_effects?(event)
    terrain = event.terrain_tag.id

    type = BridgeAwareNPCs.is_on_bridge_npc?(event) ? "ON-BRIDGE" : "UNDER-BRIDGE"

    message = "#{event.name}:\n"
    message += "  Type: #{type} (#{bridge_type})\n"
    message += "  Disabled: #{disabled}, Through: #{through}\n"
    message += "  Z-Offset: #{z_offset}\n"
    message += "  Env Effects: #{env_effects}\n"
    message += "  Terrain: #{terrain}"

    messages.push(message)
  end

  pbMessage("Bridge State: #{bridge_state} (Height: #{bridge_height})")
  pbMessage("Found #{npc_count} bridge-aware NPCs")

  messages.each { |msg| pbMessage(msg) }

  # Display cache information
  cached_count = BridgeAwareNPCs.get_bridge_npcs.length
  pbMessage("Cached NPCs: #{cached_count}")
end

# Debug function to force refresh all bridge NPCs
def pbRefreshBridgeNPCs
  BridgeAwareNPCs.invalidate_cache
  BridgeAwareNPCs.refresh_all_npcs
  pbMessage("Bridge NPCs refreshed!")
end

# Debug function to toggle bridge state and show effects
def pbToggleBridgeDebug
  if BridgeAwareNPCs.bridge_active?
    pbBridgeOff
    pbMessage("Bridge turned OFF")
  else
    pbBridgeOn
    pbMessage("Bridge turned ON")
  end
  pbTestEnhancedBridgeNPCs
end

# Function to create a test NPC with bridge awareness
def pbCreateTestBridgeNPC(name, x, y, on_bridge = true)
  return if !$game_map

  # This would typically be done in the map editor, but this function
  # demonstrates the naming convention
  npc_name = on_bridge ? "#{name}_OnZ_EnvFX" : "#{name}_OffZ_EnvFX"

  pbMessage("Test NPC should be named: #{npc_name}")
  pbMessage("Place at coordinates: (#{x}, #{y})")
  pbMessage("This NPC will be #{on_bridge ? 'on-bridge' : 'under-bridge'} with environmental effects preserved")
end

# Performance monitoring function
def pbBridgeNPCPerformanceTest
  start_time = System.uptime

  # Test cache performance
  100.times do
    BridgeAwareNPCs.refresh_all_npcs
  end

  cache_time = System.uptime - start_time

  # Test without cache
  BridgeAwareNPCs.invalidate_cache
  start_time = System.uptime

  100.times do
    BridgeAwareNPCs.refresh_all_npcs
  end

  no_cache_time = System.uptime - start_time

  pbMessage("Performance Test Results:")
  pbMessage("With cache: #{cache_time.round(3)}s")
  pbMessage("Without cache: #{no_cache_time.round(3)}s")
  pbMessage("Cache speedup: #{(no_cache_time / cache_time).round(2)}x")
end

#===============================================================================
# DOCUMENTATION AND USAGE GUIDE
#===============================================================================

=begin

ENHANCED BRIDGE-AWARE NPCs FOR POKEMON ESSENTIALS
==================================================

This script provides comprehensive management of NPC behavior and rendering
based on bridge states in Pokemon Essentials for RPG Maker XP.

NAMING CONVENTIONS:
------------------

1. ON-BRIDGE NPCs (appear on top of bridges):
   - Include "OnZ" or "z100" in the event name
   - Examples: "Fisherman_OnZ", "Trainer_z100", "Guard_OnZ_EnvFX"

2. UNDER-BRIDGE NPCs (appear below bridges):
   - Include "OffZ" or "z0" in the event name
   - Examples: "Swimmer_OffZ", "Boater_z0", "Fisher_OffZ_EnvFX"

3. ENVIRONMENTAL EFFECTS PRESERVATION:
   - Add "EnvFX", "GrassFX", or "WaterFX" to preserve terrain effects
   - Examples: "NPC_OnZ_GrassFX", "Trainer_OffZ_WaterFX"

BEHAVIOR:
---------

WHEN BRIDGE IS ON (pbBridgeOn with height):
- On-bridge NPCs: Interactable, solid, elevated by (height + 1) levels
- Under-bridge NPCs: Interactable, solid, positioned below bridge surface
- Player: Elevated by bridge height when on bridge tiles

WHEN BRIDGE IS OFF (pbBridgeOff):
- On-bridge NPCs: Non-interactable, walkthrough, floating high above
- Under-bridge NPCs: Interactable, solid, normal ground level

Z-LEVEL CALCULATION:
- Bridge height multiplier: 32 pixels per level (standard tile height)
- On-bridge active: (bridge_height + 1) × 32 + 32 base offset
- On-bridge inactive: (bridge_height + 3) × 32 + 32 base offset
- Under-bridge active: -32 pixels (below surface)
- Under-bridge inactive: 0 pixels (ground level)

FEATURES:
---------

✓ Automatic NPC detection via naming conventions
✓ Proper z-ordering hierarchy for complex bridge scenarios
✓ Environmental effects preservation (grass, water, etc.)
✓ Performance optimization with intelligent caching
✓ Integration with existing Pokemon Essentials systems
✓ Comprehensive debugging and testing utilities

TESTING FUNCTIONS:
-----------------

pbTestEnhancedBridgeNPCs    - Display detailed NPC information
pbRefreshBridgeNPCs         - Force refresh all bridge NPCs
pbToggleBridgeDebug         - Toggle bridge state and show effects
pbCreateTestBridgeNPC       - Helper for creating test NPCs
pbBridgeNPCPerformanceTest  - Performance monitoring

INTEGRATION:
-----------

The script automatically hooks into:
- pbBridgeOn/pbBridgeOff functions
- Game_Event refresh and interaction systems
- Environmental effect rendering
- Map change events
- Game save/load events

No additional setup required - just name your NPCs correctly!

=end