#===============================================================================
# Bridge-Aware NPCs Script
# Automatically adjusts NPC behavior based on bridge level
#===============================================================================

module BridgeAwareNPCs
  # Constants for z-level adjustments
  BRIDGE_HEIGHT_OFFSET = 180 # Pixels above bridge level when bridge is off
  BRIDGE_ACTIVE_HEIGHT = 164  # Pixels above ground when bridge is active (raised by 2 levels)
  UNDER_BRIDGE_Z = -64        # Z-level for NPCs under bridges (below bridge surface)
  
  # Check if an event has bridge-aware naming
  def self.is_bridge_aware?(event)
    return false if !event || !event.name
    name = event.name.downcase
    # Check for z-level naming patterns
    return true if name.include?("z100") || name.include?("z0")
    return true if name.include?("onz") || name.include?("offz")
    return false
  end
  
  # Determine if NPC should be "on" the bridge based on naming
  def self.is_on_bridge_npc?(event)
    return false if !event || !event.name
    name = event.name.downcase
    # NPCs with z100 or OnZ are considered "on bridge" NPCs
    return true if name.include?("z100") || name.include?("onz")
    return false
  end
  
  # Determine if NPC should be "under" the bridge based on naming
  def self.is_under_bridge_npc?(event)
    return false if !event || !event.name
    name = event.name.downcase
    # NPCs with z0 or OffZ are considered "under bridge" NPCs
    return true if name.include?("z0") || name.include?("offz")
    return false
  end
  
  # Update NPC properties based on bridge state
  def self.update_npc_for_bridge(event)
    return if !is_bridge_aware?(event)
    return if !$PokemonGlobal || !$PokemonGlobal.respond_to?(:bridge)

    bridge_active = $PokemonGlobal.bridge && $PokemonGlobal.bridge > 0

    if is_on_bridge_npc?(event)
      # NPC that should be on the bridge
      if bridge_active
        # Bridge is on - NPC is interactable at bridge level (slightly elevated)
        event.through = false
        event.instance_variable_set(:@bridge_z_offset, BRIDGE_ACTIVE_HEIGHT)
        event.instance_variable_set(:@bridge_disabled, false)
      else
        # Bridge is off - NPC is still above bridge level but non-interactable and walkable
        event.through = true
        event.instance_variable_set(:@bridge_z_offset, BRIDGE_HEIGHT_OFFSET)
        event.instance_variable_set(:@bridge_disabled, true)
      end
    elsif is_under_bridge_npc?(event)
      # NPC that should be under the bridge (z0 NPCs are below bridge when active)
      if bridge_active
        # Bridge is on/lowered - NPC is below the bridge level (interactable but lower z)
        event.through = false
        event.instance_variable_set(:@bridge_z_offset, UNDER_BRIDGE_Z)
        event.instance_variable_set(:@bridge_disabled, false)
      else
        # Bridge is off/raised - NPC is interactable and solid at normal level
        event.through = false
        event.instance_variable_set(:@bridge_z_offset, 0)
        event.instance_variable_set(:@bridge_disabled, false)
      end
    end
  end
  
  # Refresh all bridge-aware NPCs on the current map
  def self.refresh_all_npcs
    return if !$game_map || !$game_map.events
    $game_map.events.each_value do |event|
      next if !is_bridge_aware?(event)
      update_npc_for_bridge(event)
      # Force the event to refresh its page conditions
      event.refresh if event.respond_to?(:refresh)
    end
    # Force spriteset refresh to update visual changes
    if $scene.is_a?(Scene_Map) && $scene.respond_to?(:spriteset) && $scene.spriteset
      $scene.spriteset.refresh_characters if $scene.spriteset.respond_to?(:refresh_characters)
    end
  end
end

#===============================================================================
# Override pbBridgeOn to refresh NPCs
#===============================================================================
if defined?(pbBridgeOn)
  alias __bridge_npcs_pbBridgeOn pbBridgeOn
  def pbBridgeOn(height = 2)
    __bridge_npcs_pbBridgeOn(height)
    BridgeAwareNPCs.refresh_all_npcs
  end
end

#===============================================================================
# Override pbBridgeOff to refresh NPCs
#===============================================================================
if defined?(pbBridgeOff)
  alias __bridge_npcs_pbBridgeOff pbBridgeOff
  def pbBridgeOff
    __bridge_npcs_pbBridgeOff
    BridgeAwareNPCs.refresh_all_npcs
  end
end

#===============================================================================
# Extend Game_Event to handle bridge-aware z-levels and interactions
#===============================================================================
class Game_Event
  # Override refresh to update bridge behavior
  alias __bridge_npcs_refresh refresh
  def refresh
    __bridge_npcs_refresh
    BridgeAwareNPCs.update_npc_for_bridge(self)
  end

  # Override start to prevent interaction when bridge-disabled
  alias __bridge_npcs_start start
  def start
    # Don't start if this NPC is bridge-disabled
    return if @bridge_disabled
    __bridge_npcs_start
  end

  # Override check_event_trigger_touch to prevent touch triggers when disabled
  alias __bridge_npcs_check_event_trigger_touch check_event_trigger_touch
  def check_event_trigger_touch(dir)
    return if @bridge_disabled
    __bridge_npcs_check_event_trigger_touch(dir)
  end

  # Override check_event_trigger_auto to prevent auto triggers when disabled
  alias __bridge_npcs_check_event_trigger_auto check_event_trigger_auto
  def check_event_trigger_auto
    return if @bridge_disabled
    __bridge_npcs_check_event_trigger_auto
  end
end

#===============================================================================
# Extend Game_Character to handle proper z-ordering for bridge NPCs and player
#===============================================================================
class Game_Character
  alias __bridge_npcs_screen_z screen_z
  def screen_z(height = 0)
    base_z = __bridge_npcs_screen_z(height)

    # Special handling for bridge-aware events
    if self.is_a?(Game_Event) && BridgeAwareNPCs.is_bridge_aware?(self)
      bridge_offset = @bridge_z_offset || 0
      return base_z + bridge_offset
    end

    # Special handling for player when bridge is active
    if self.is_a?(Game_Player) && 
       $PokemonGlobal && $PokemonGlobal.respond_to?(:bridge) && 
       $PokemonGlobal.bridge && $PokemonGlobal.bridge > 0
      return base_z + BridgeAwareNPCs::BRIDGE_ACTIVE_HEIGHT
    end

    return base_z
  end
end

#===============================================================================
# Extend Game_Player to handle passability with bridge-disabled NPCs
#===============================================================================
class Game_Player
  alias __bridge_npcs_passable? passable?
  def passable?(x, y, d, strict = false)
    # Check normal passability first
    return false unless __bridge_npcs_passable?(x, y, d, strict)

    # Check if there are any bridge-disabled events at the target position
    new_x = x + (d == 6 ? 1 : d == 4 ? -1 : 0)
    new_y = y + (d == 2 ? 1 : d == 8 ? -1 : 0)

    return true if !$game_map || !$game_map.events

    $game_map.events.each_value do |event|
      # Check if event is at the target coordinates
      next if event.x != new_x || event.y != new_y
      next if !BridgeAwareNPCs.is_bridge_aware?(event)
      # If event is bridge-disabled, it should be walkable
      next if event.instance_variable_get(:@bridge_disabled)
      # If event is not bridge-disabled and not through, block movement
      return false if !event.through && event.character_name != ""
    end

    return true
  end
end

#===============================================================================
# Initialize bridge-aware NPCs when map loads
#===============================================================================
if defined?(EventHandlers)
  EventHandlers.add(:on_enter_map, :refresh_bridge_npcs,
    proc {
      BridgeAwareNPCs.refresh_all_npcs
    }
  )
end

#===============================================================================
# Debug functions to manually test bridge NPCs (optional)
#===============================================================================
def pbRefreshBridgeNPCs
  BridgeAwareNPCs.refresh_all_npcs
  pbMessage("Bridge-aware NPCs refreshed!")
end

def pbTestBridgeNPCs
  return if !$game_map || !$game_map.events
  
  bridge_state = ($PokemonGlobal && $PokemonGlobal.respond_to?(:bridge) && 
                  $PokemonGlobal.bridge && $PokemonGlobal.bridge > 0) ? "ON" : "OFF"
  npc_count = 0

  $game_map.events.each_value do |event|
    next if !BridgeAwareNPCs.is_bridge_aware?(event)
    npc_count += 1
    disabled = event.instance_variable_get(:@bridge_disabled)
    through = event.through
    z_offset = event.instance_variable_get(:@bridge_z_offset) || 0

    type = BridgeAwareNPCs.is_on_bridge_npc?(event) ? "ON-BRIDGE" : "UNDER-BRIDGE"

    pbMessage("#{event.name}: #{type}, Disabled: #{disabled}, Through: #{through}, Z-Offset: #{z_offset}")
  end

  pbMessage("Bridge: #{bridge_state}, Found #{npc_count} bridge-aware NPCs")
end
